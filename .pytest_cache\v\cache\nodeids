["tests/test_comprehensive_game_engine.py::TestAvanceTempo::test_avanco_tempo", "tests/test_comprehensive_game_engine.py::TestAvanceTempo::test_recuperacao_lesao", "tests/test_comprehensive_game_engine.py::TestContrato::test_contrato_valido", "tests/test_comprehensive_game_engine.py::TestContrato::test_contrato_vencido", "tests/test_comprehensive_game_engine.py::TestEdgeCases::test_calcular_forca_time_vazio", "tests/test_comprehensive_game_engine.py::TestEdgeCases::test_carregar_dados_api_real", "tests/test_comprehensive_game_engine.py::TestEdgeCases::test_carregar_dados_com_erro", "tests/test_comprehensive_game_engine.py::TestEdgeCases::test_jogador_com_atributos_extremos", "tests/test_comprehensive_game_engine.py::TestEdgeCases::test_liga_com_numero_impar_times", "tests/test_comprehensive_game_engine.py::TestEdgeCases::test_partida_ja_finalizada", "tests/test_comprehensive_game_engine.py::TestEdgeCases::test_simulacao_rodada_inexistente", "tests/test_comprehensive_game_engine.py::TestEdgeCases::test_time_sem_jogadores_escalacao", "tests/test_comprehensive_game_engine.py::TestFormacoes::test_formacoes_predefinidas", "tests/test_comprehensive_game_engine.py::TestFormacoes::test_validacao_formacao", "tests/test_comprehensive_game_engine.py::TestJogador::test_criacao_jogador_basico", "tests/test_comprehensive_game_engine.py::TestJogador::test_disponibilidade_jogador", "tests/test_comprehensive_game_engine.py::TestJogador::test_media_geral_goleiro", "tests/test_comprehensive_game_engine.py::TestJogador::test_media_geral_jogador_campo", "tests/test_comprehensive_game_engine.py::TestLiga::test_criacao_liga", "tests/test_comprehensive_game_engine.py::TestLiga::test_geracao_calendario", "tests/test_comprehensive_game_engine.py::TestLiga::test_tabela_classificacao", "tests/test_comprehensive_game_engine.py::TestMotorJogo::test_criacao_liga_exemplo", "tests/test_comprehensive_game_engine.py::TestMotorJogo::test_criacao_motor", "tests/test_comprehensive_game_engine.py::TestMotorJogo::test_geracao_elenco_completo", "tests/test_comprehensive_game_engine.py::TestMotorJogo::test_geracao_jogador", "tests/test_comprehensive_game_engine.py::TestPartida::test_criacao_partida", "tests/test_comprehensive_game_engine.py::TestPartida::test_simulacao_partida", "tests/test_comprehensive_game_engine.py::TestPartida::test_simulacao_rodada", "tests/test_comprehensive_game_engine.py::TestSaveLoad::test_salvar_carregar_jogo", "tests/test_comprehensive_game_engine.py::TestTime::test_criacao_time", "tests/test_comprehensive_game_engine.py::TestTime::test_escalacao_titular", "tests/test_comprehensive_game_engine.py::TestTime::test_estatisticas_time", "tests/test_data_loader.py::TestCarregadorDados::test_criacao_carregador", "tests/test_data_loader.py::TestCarregadorDados::test_obter_classificacao_uefa", "tests/test_data_loader.py::TestCarregadorDados::test_obter_clubes_liga_erro", "tests/test_data_loader.py::TestCarregadorDados::test_obter_clubes_liga_sucesso", "tests/test_game_engine.py::TestMotorJogo::test_criacao_time", "tests/test_game_engine.py::TestMotorJogo::test_criacao_time_completo", "tests/test_game_engine.py::TestMotorJogo::test_geracao_habilidades", "tests/test_game_engine.py::TestMotorJogo::test_geracao_jogador_atributos"]