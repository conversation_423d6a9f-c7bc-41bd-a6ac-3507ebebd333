import pygame
import random
from dataclasses import dataclass
from elifoot2025.data_loader import CarregadorDados

@dataclass
class Jogador:
    nome: str
    posicao: str
    forca: int
    velocidade: int
    tecnica: int

@dataclass
class Time:
    nome: str
    forca_time: int
    jogadores: list[Jogador]

class MotorJogo:
    def __init__(self, carregador_dados=None):
        self.times = []
        self.rodada_atual = 0
        self.carregador_dados = carregador_dados or CarregadorDados("")
    
    def carregar_dados(self, liga_id):
        """Carrega dados de clubes de fonte online usando o CarregadorDados"""
        clubs_data = self.carregador_dados.obter_clubes_liga(liga_id)
        for club in clubs_data:
            habilidades = self.gerar_habilidades(club['forca'])
            self.times.append(Time(
                nome=club['nome'],
                forca_time=club['forca'],
                jogadores=[Jogador(nome=j['nome'], posicao=j['posicao'], **habilidades) 
                          for j in club['jogadores']]
            ))
    
    def gerar_habilidades(self, forca_time: int) -> dict[str, int]:
        """Gera habilidades de jogadores com base na força do time (1-100)"""
        # Garante que a força do time está dentro dos limites válidos
        forca_time = max(1, min(100, forca_time))
        
        # Usa distribuição normal para habilidades mais realistas
        return {
            'forca': max(1, min(100, int(random.gauss(
                mu=forca_time,
                sigma=forca_time/10
            )))),
            'velocidade': max(1, min(100, int(random.gauss(
                mu=max(40, forca_time - 10),
                sigma=forca_time/12
            )))),
            'tecnica': max(1, min(100, int(random.gauss(
                mu=max(30, forca_time - 15),
                sigma=forca_time/15
            ))))
        }
