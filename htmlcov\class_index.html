<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">96%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 23:01 -0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2___init___py.html">elifoot2025\__init__.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html#t4">elifoot2025\data_loader.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html#t4"><data value='CarregadorDados'>CarregadorDados</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html">elifoot2025\data_loader.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t10">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t10"><data value='Posicao'>Posicao</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t24">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t24"><data value='StatusJogador'>StatusJogador</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t33">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t33"><data value='Contrato'>Contrato</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t51">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t51"><data value='Jogador'>Jogador</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t126">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t126"><data value='Formacao'>Formacao</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t163">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t163"><data value='Time'>Time</data></a></td>
                <td>23</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="19 23">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t247">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t247"><data value='Partida'>Partida</data></a></td>
                <td>10</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="4 10">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t276">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t276"><data value='Liga'>Liga</data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t352">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t352"><data value='MotorJogo'>MotorJogo</data></a></td>
                <td>192</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="186 192">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>140</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="140 140">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>428</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="412 428">96%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 23:01 -0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
