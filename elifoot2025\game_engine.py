import random
import json
import datetime
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Tuple
from enum import Enum
from elifoot2025.data_loader import CarregadorDados


class Posicao(Enum):
    """Posições dos jogadores"""
    GOLEIRO = "GK"
    ZAGUEIRO = "CB"
    LATERAL_DIREITO = "RB"
    LATERAL_ESQUERDO = "LB"
    VOLANTE = "CDM"
    MEIO_CAMPO = "CM"
    MEIO_ATACANTE = "CAM"
    PONTA_DIREITA = "RW"
    PONTA_ESQUERDA = "LW"
    ATACANTE = "ST"


class StatusJogador(Enum):
    """Status do jogador"""
    ATIVO = "active"
    LESIONADO = "injured"
    SUSPENSO = "suspended"
    EMPRESTADO = "loaned"


@dataclass
class Contrato:
    """Contrato do jogador"""
    salario_semanal: int
    data_inicio: datetime.date
    data_fim: datetime.date
    clausula_rescisao: int

    def esta_vencido(self) -> bool:
        return datetime.date.today() > self.data_fim

    def semanas_restantes(self) -> int:
        if self.esta_vencido():
            return 0
        delta = self.data_fim - datetime.date.today()
        return delta.days // 7


@dataclass
class Jogador:
    """Jogador com atributos completos"""
    nome: str
    idade: int
    posicao: Posicao
    nacionalidade: str

    # Atributos técnicos
    finalizacao: int = 50
    passe: int = 50
    drible: int = 50
    primeiro_toque: int = 50
    cruzamento: int = 50

    # Atributos físicos
    velocidade: int = 50
    aceleracao: int = 50
    forca: int = 50
    resistencia: int = 50
    agilidade: int = 50

    # Atributos mentais
    concentracao: int = 50
    decisao: int = 50
    lideranca: int = 50
    trabalho_equipe: int = 50

    # Atributos específicos do goleiro
    reflexos: int = 50
    posicionamento_gk: int = 50
    manuseio: int = 50

    # Status e informações
    status: StatusJogador = StatusJogador.ATIVO
    valor_mercado: int = 100000
    contrato: Optional[Contrato] = None
    lesao_dias: int = 0
    suspensao_jogos: int = 0
    moral: int = 75
    forma: int = 75

    def __post_init__(self):
        if self.contrato is None:
            # Contrato padrão de 2 anos
            self.contrato = Contrato(
                salario_semanal=max(1000, self.valor_mercado // 1000),
                data_inicio=datetime.date.today(),
                data_fim=datetime.date.today() + datetime.timedelta(days=730),
                clausula_rescisao=self.valor_mercado * 2
            )

    def media_geral(self) -> int:
        """Calcula a média geral do jogador baseada na posição"""
        if self.posicao == Posicao.GOLEIRO:
            return int((self.reflexos + self.posicionamento_gk + self.manuseio +
                       self.concentracao + self.decisao) / 5)

        # Para jogadores de campo
        tecnico = (self.finalizacao + self.passe + self.drible +
                  self.primeiro_toque + self.cruzamento) / 5
        fisico = (self.velocidade + self.aceleracao + self.forca +
                 self.resistencia + self.agilidade) / 5
        mental = (self.concentracao + self.decisao + self.lideranca +
                 self.trabalho_equipe) / 4

        return int((tecnico + fisico + mental) / 3)

    def esta_disponivel(self) -> bool:
        """Verifica se o jogador está disponível para jogar"""
        return (self.status == StatusJogador.ATIVO and
                self.lesao_dias == 0 and
                self.suspensao_jogos == 0)


@dataclass
class Formacao:
    """Formação tática do time"""
    nome: str
    defensores: int
    meio_campistas: int
    atacantes: int
    posicoes: List[Tuple[int, int]]  # Posições no campo (x, y)

    def __post_init__(self):
        if self.defensores + self.meio_campistas + self.atacantes != 10:
            raise ValueError("Formação deve ter exatamente 10 jogadores de campo")


# Formações pré-definidas
FORMACOES = {
    "4-4-2": Formacao("4-4-2", 4, 4, 2, [
        (50, 10),  # GK
        (20, 25), (35, 25), (65, 25), (80, 25),  # Defesa
        (20, 50), (35, 50), (65, 50), (80, 50),  # Meio-campo
        (40, 75), (60, 75)  # Ataque
    ]),
    "4-3-3": Formacao("4-3-3", 4, 3, 3, [
        (50, 10),  # GK
        (20, 25), (35, 25), (65, 25), (80, 25),  # Defesa
        (30, 50), (50, 50), (70, 50),  # Meio-campo
        (25, 75), (50, 75), (75, 75)  # Ataque
    ]),
    "3-5-2": Formacao("3-5-2", 3, 5, 2, [
        (50, 10),  # GK
        (30, 25), (50, 25), (70, 25),  # Defesa
        (15, 45), (35, 50), (50, 50), (65, 50), (85, 45),  # Meio-campo
        (40, 75), (60, 75)  # Ataque
    ])
}


@dataclass
class Time:
    """Time com informações completas"""
    nome: str
    cidade: str
    pais: str
    fundacao: int
    estadio: str
    capacidade_estadio: int
    jogadores: List[Jogador] = field(default_factory=list)
    formacao: Formacao = field(default_factory=lambda: FORMACOES["4-4-2"])

    # Finanças
    orcamento: int = 10000000
    salarios_semanais: int = 0
    dividas: int = 0

    # Estatísticas da temporada
    jogos: int = 0
    vitorias: int = 0
    empates: int = 0
    derrotas: int = 0
    gols_marcados: int = 0
    gols_sofridos: int = 0

    def __post_init__(self):
        self.calcular_salarios()

    def calcular_salarios(self):
        """Calcula o total de salários semanais"""
        self.salarios_semanais = sum(j.contrato.salario_semanal for j in self.jogadores)

    def pontos(self) -> int:
        """Calcula pontos na tabela"""
        return self.vitorias * 3 + self.empates

    def saldo_gols(self) -> int:
        """Calcula saldo de gols"""
        return self.gols_marcados - self.gols_sofridos

    def media_idade(self) -> float:
        """Calcula média de idade do elenco"""
        if not self.jogadores:
            return 0
        return sum(j.idade for j in self.jogadores) / len(self.jogadores)

    def valor_elenco(self) -> int:
        """Calcula valor total do elenco"""
        return sum(j.valor_mercado for j in self.jogadores)

    def jogadores_por_posicao(self, posicao: Posicao) -> List[Jogador]:
        """Retorna jogadores de uma posição específica"""
        return [j for j in self.jogadores if j.posicao == posicao]

    def escalacao_titular(self) -> List[Jogador]:
        """Retorna a melhor escalação baseada na formação"""
        escalacao = []

        # Goleiro
        goleiros = self.jogadores_por_posicao(Posicao.GOLEIRO)
        if goleiros:
            escalacao.append(max(goleiros, key=lambda j: j.media_geral()))

        # Defensores
        defensores = [j for j in self.jogadores if j.posicao in
                     [Posicao.ZAGUEIRO, Posicao.LATERAL_DIREITO, Posicao.LATERAL_ESQUERDO]]
        defensores.sort(key=lambda j: j.media_geral(), reverse=True)
        escalacao.extend(defensores[:self.formacao.defensores])

        # Meio-campistas
        meio_campistas = [j for j in self.jogadores if j.posicao in
                         [Posicao.VOLANTE, Posicao.MEIO_CAMPO, Posicao.MEIO_ATACANTE]]
        meio_campistas.sort(key=lambda j: j.media_geral(), reverse=True)
        escalacao.extend(meio_campistas[:self.formacao.meio_campistas])

        # Atacantes
        atacantes = [j for j in self.jogadores if j.posicao in
                    [Posicao.ATACANTE, Posicao.PONTA_DIREITA, Posicao.PONTA_ESQUERDA]]
        atacantes.sort(key=lambda j: j.media_geral(), reverse=True)
        escalacao.extend(atacantes[:self.formacao.atacantes])

        return escalacao[:11]  # Máximo 11 jogadores


@dataclass
class Partida:
    """Representa uma partida"""
    time_casa: Time
    time_visitante: Time
    data: datetime.date
    gols_casa: int = 0
    gols_visitante: int = 0
    finalizada: bool = False
    escalacao_casa: List[Jogador] = field(default_factory=list)
    escalacao_visitante: List[Jogador] = field(default_factory=list)

    def resultado(self) -> str:
        """Retorna resultado da partida"""
        if not self.finalizada:
            return "Não finalizada"
        return f"{self.time_casa.nome} {self.gols_casa} x {self.gols_visitante} {self.time_visitante.nome}"

    def vencedor(self) -> Optional[Time]:
        """Retorna o time vencedor ou None em caso de empate"""
        if not self.finalizada:
            return None
        if self.gols_casa > self.gols_visitante:
            return self.time_casa
        elif self.gols_visitante > self.gols_casa:
            return self.time_visitante
        return None


@dataclass
class Liga:
    """Liga/Campeonato"""
    nome: str
    pais: str
    nivel: int  # 1 = primeira divisão, 2 = segunda, etc.
    times: List[Time] = field(default_factory=list)
    rodadas: List[List[Partida]] = field(default_factory=list)
    rodada_atual: int = 0
    temporada: int = 2025

    def gerar_tabela_classificacao(self) -> List[Dict]:
        """Gera tabela de classificação"""
        tabela = []
        for time in self.times:
            tabela.append({
                'time': time.nome,
                'jogos': time.jogos,
                'vitorias': time.vitorias,
                'empates': time.empates,
                'derrotas': time.derrotas,
                'gols_marcados': time.gols_marcados,
                'gols_sofridos': time.gols_sofridos,
                'saldo_gols': time.saldo_gols(),
                'pontos': time.pontos()
            })

        # Ordena por pontos, saldo de gols e gols marcados
        tabela.sort(key=lambda x: (x['pontos'], x['saldo_gols'], x['gols_marcados']), reverse=True)

        # Adiciona posição
        for i, entrada in enumerate(tabela):
            entrada['posicao'] = i + 1

        return tabela

    def gerar_calendario(self):
        """Gera calendário de jogos (todos contra todos em turno e returno)"""
        self.rodadas = []
        times = self.times.copy()
        n_times = len(times)

        if n_times % 2 == 1:
            times.append(None)  # Time fantasma para número ímpar de times
            n_times += 1

        # Gera turno
        for rodada in range(n_times - 1):
            partidas_rodada = []
            for i in range(n_times // 2):
                time1 = times[i]
                time2 = times[n_times - 1 - i]

                if time1 and time2:  # Ignora jogos com time fantasma
                    data_jogo = datetime.date.today() + datetime.timedelta(days=rodada * 7)
                    partidas_rodada.append(Partida(time1, time2, data_jogo))

            if partidas_rodada:
                self.rodadas.append(partidas_rodada)

            # Rotaciona times (exceto o primeiro)
            times = [times[0]] + [times[-1]] + times[1:-1]

        # Gera returno (inverte mando de campo)
        turno_rodadas = len(self.rodadas)
        for i in range(turno_rodadas):
            partidas_returno = []
            for partida in self.rodadas[i]:
                data_jogo = datetime.date.today() + datetime.timedelta(days=(turno_rodadas + i) * 7)
                partidas_returno.append(Partida(
                    partida.time_visitante,
                    partida.time_casa,
                    data_jogo
                ))
            self.rodadas.append(partidas_returno)


class MotorJogo:
    """Motor principal do jogo"""

    def __init__(self, carregador_dados=None):
        self.ligas: List[Liga] = []
        self.time_jogador: Optional[Time] = None
        self.data_atual = datetime.date.today()
        self.carregador_dados = carregador_dados or CarregadorDados("")
        self.temporada_atual = 2025

    def carregar_dados(self, liga_id: str):
        """Carrega dados de clubes de fonte online usando o CarregadorDados"""
        try:
            clubs_data = self.carregador_dados.obter_clubes_liga(liga_id)
            liga = Liga(f"Liga {liga_id}", "Brasil", 1)

            for club_data in clubs_data:
                time = self.criar_time_com_jogadores(club_data)
                liga.times.append(time)

            liga.gerar_calendario()
            self.ligas.append(liga)

        except Exception as e:
            print(f"Erro ao carregar dados: {e}")
            # Cria liga de exemplo se falhar
            self.criar_liga_exemplo()

    def criar_liga_exemplo(self):
        """Cria uma liga de exemplo para testes"""
        liga = Liga("Liga Exemplo", "Brasil", 1)

        times_exemplo = [
            "Flamengo", "Palmeiras", "São Paulo", "Corinthians",
            "Santos", "Grêmio", "Internacional", "Atlético-MG",
            "Fluminense", "Botafogo", "Vasco", "Cruzeiro",
            "Bahia", "Sport", "Ceará", "Fortaleza",
            "Goiás", "Coritiba", "Athletico-PR", "Bragantino"
        ]

        for nome_time in times_exemplo:
            time = Time(
                nome=nome_time,
                cidade=nome_time,
                pais="Brasil",
                fundacao=1900,
                estadio=f"Estádio {nome_time}",
                capacidade_estadio=40000
            )

            # Gera elenco completo
            time.jogadores = self.gerar_elenco_completo(nome_time)
            time.calcular_salarios()
            liga.times.append(time)

        liga.gerar_calendario()
        self.ligas.append(liga)

    def criar_time_com_jogadores(self, club_data: Dict) -> Time:
        """Cria um time com jogadores baseado nos dados do clube"""
        time = Time(
            nome=club_data.get('name', 'Time Desconhecido'),
            cidade=club_data.get('area', {}).get('name', 'Cidade Desconhecida'),
            pais=club_data.get('area', {}).get('name', 'País Desconhecido'),
            fundacao=club_data.get('founded', 1900),
            estadio=club_data.get('venue', 'Estádio Desconhecido'),
            capacidade_estadio=30000
        )

        # Gera força do time baseada em dados reais ou aleatória
        forca_time = random.randint(60, 90)
        time.jogadores = self.gerar_elenco_completo(time.nome, forca_time)
        time.calcular_salarios()

        return time

    def gerar_elenco_completo(self, nome_time: str, forca_base: int = 75) -> List[Jogador]:
        """Gera um elenco completo com jogadores realistas"""
        jogadores = []

        # Nomes brasileiros comuns para gerar jogadores
        nomes = [
            "Gabriel", "Lucas", "Matheus", "Rafael", "Felipe", "Bruno", "Diego", "Carlos",
            "André", "João", "Pedro", "Thiago", "Rodrigo", "Marcelo", "Daniel", "Gustavo",
            "Leonardo", "Fernando", "Ricardo", "Alexandre", "Vinícius", "Henrique", "Caio",
            "Fabio", "Renato", "Leandro", "Marcos", "Paulo", "Júlio", "Antônio"
        ]

        sobrenomes = [
            "Silva", "Santos", "Oliveira", "Souza", "Rodrigues", "Ferreira", "Alves",
            "Pereira", "Lima", "Gomes", "Costa", "Ribeiro", "Martins", "Carvalho",
            "Almeida", "Lopes", "Soares", "Fernandes", "Vieira", "Barbosa", "Rocha",
            "Dias", "Monteiro", "Cardoso", "Reis", "Araújo", "Nascimento", "Freitas"
        ]

        # Goleiros (3)
        for i in range(3):
            nome = f"{random.choice(nomes)} {random.choice(sobrenomes)}"
            jogador = self.gerar_jogador(nome, Posicao.GOLEIRO, forca_base)
            jogadores.append(jogador)

        # Defensores (8)
        posicoes_defesa = [
            Posicao.ZAGUEIRO, Posicao.ZAGUEIRO, Posicao.ZAGUEIRO, Posicao.ZAGUEIRO,
            Posicao.LATERAL_DIREITO, Posicao.LATERAL_DIREITO,
            Posicao.LATERAL_ESQUERDO, Posicao.LATERAL_ESQUERDO
        ]
        for posicao in posicoes_defesa:
            nome = f"{random.choice(nomes)} {random.choice(sobrenomes)}"
            jogador = self.gerar_jogador(nome, posicao, forca_base)
            jogadores.append(jogador)

        # Meio-campistas (8)
        posicoes_meio = [
            Posicao.VOLANTE, Posicao.VOLANTE,
            Posicao.MEIO_CAMPO, Posicao.MEIO_CAMPO, Posicao.MEIO_CAMPO,
            Posicao.MEIO_ATACANTE, Posicao.MEIO_ATACANTE,
            Posicao.MEIO_CAMPO
        ]
        for posicao in posicoes_meio:
            nome = f"{random.choice(nomes)} {random.choice(sobrenomes)}"
            jogador = self.gerar_jogador(nome, posicao, forca_base)
            jogadores.append(jogador)

        # Atacantes (6)
        posicoes_ataque = [
            Posicao.ATACANTE, Posicao.ATACANTE, Posicao.ATACANTE,
            Posicao.PONTA_DIREITA, Posicao.PONTA_ESQUERDA,
            Posicao.ATACANTE
        ]
        for posicao in posicoes_ataque:
            nome = f"{random.choice(nomes)} {random.choice(sobrenomes)}"
            jogador = self.gerar_jogador(nome, posicao, forca_base)
            jogadores.append(jogador)

        return jogadores

    def gerar_jogador(self, nome: str, posicao: Posicao, forca_base: int) -> Jogador:
        """Gera um jogador com atributos baseados na posição e força do time"""
        # Variação na força individual do jogador
        variacao = random.randint(-15, 15)
        forca_jogador = max(30, min(95, forca_base + variacao))

        # Idade realista
        idade = random.randint(18, 35)
        if idade < 22:  # Jovens têm potencial maior mas habilidades menores
            forca_jogador = max(30, forca_jogador - 10)
        elif idade > 30:  # Veteranos têm experiência mas podem estar em declínio
            forca_jogador = max(40, forca_jogador - 5)

        jogador = Jogador(
            nome=nome,
            idade=idade,
            posicao=posicao,
            nacionalidade="Brasil"
        )

        # Gera atributos baseados na posição
        if posicao == Posicao.GOLEIRO:
            jogador.reflexos = self._gerar_atributo(forca_jogador, 10)
            jogador.posicionamento_gk = self._gerar_atributo(forca_jogador, 8)
            jogador.manuseio = self._gerar_atributo(forca_jogador, 8)
            jogador.concentracao = self._gerar_atributo(forca_jogador, 5)
            jogador.decisao = self._gerar_atributo(forca_jogador, 5)
        else:
            # Atributos técnicos
            jogador.finalizacao = self._gerar_atributo_posicional(forca_jogador, posicao, "finalizacao")
            jogador.passe = self._gerar_atributo_posicional(forca_jogador, posicao, "passe")
            jogador.drible = self._gerar_atributo_posicional(forca_jogador, posicao, "drible")
            jogador.primeiro_toque = self._gerar_atributo(forca_jogador, 8)
            jogador.cruzamento = self._gerar_atributo_posicional(forca_jogador, posicao, "cruzamento")

            # Atributos físicos
            jogador.velocidade = self._gerar_atributo_posicional(forca_jogador, posicao, "velocidade")
            jogador.aceleracao = self._gerar_atributo_posicional(forca_jogador, posicao, "aceleracao")
            jogador.forca = self._gerar_atributo_posicional(forca_jogador, posicao, "forca")
            jogador.resistencia = self._gerar_atributo(forca_jogador, 8)
            jogador.agilidade = self._gerar_atributo_posicional(forca_jogador, posicao, "agilidade")

            # Atributos mentais
            jogador.concentracao = self._gerar_atributo(forca_jogador, 8)
            jogador.decisao = self._gerar_atributo(forca_jogador, 8)
            jogador.lideranca = self._gerar_atributo(forca_jogador, 12)
            jogador.trabalho_equipe = self._gerar_atributo(forca_jogador, 6)

        # Calcula valor de mercado baseado na idade e habilidades
        media = jogador.media_geral()
        multiplicador_idade = 1.0
        if idade < 25:
            multiplicador_idade = 1.5  # Jovens valem mais
        elif idade > 30:
            multiplicador_idade = 0.7  # Veteranos valem menos

        jogador.valor_mercado = int(media * 50000 * multiplicador_idade)

        return jogador

    def _gerar_atributo(self, base: int, variacao: int) -> int:
        """Gera um atributo com variação"""
        return max(1, min(99, int(random.gauss(base, variacao))))

    def _gerar_atributo_posicional(self, base: int, posicao: Posicao, atributo: str) -> int:
        """Gera atributo considerando a importância para a posição"""
        # Multiplicadores por posição para diferentes atributos
        multiplicadores = {
            Posicao.ZAGUEIRO: {
                "finalizacao": 0.3, "passe": 0.8, "drible": 0.5, "cruzamento": 0.4,
                "velocidade": 0.7, "aceleracao": 0.7, "forca": 1.2, "agilidade": 0.8
            },
            Posicao.LATERAL_DIREITO: {
                "finalizacao": 0.5, "passe": 1.0, "drible": 0.9, "cruzamento": 1.2,
                "velocidade": 1.1, "aceleracao": 1.1, "forca": 0.8, "agilidade": 1.0
            },
            Posicao.LATERAL_ESQUERDO: {
                "finalizacao": 0.5, "passe": 1.0, "drible": 0.9, "cruzamento": 1.2,
                "velocidade": 1.1, "aceleracao": 1.1, "forca": 0.8, "agilidade": 1.0
            },
            Posicao.VOLANTE: {
                "finalizacao": 0.6, "passe": 1.2, "drible": 0.8, "cruzamento": 0.6,
                "velocidade": 0.8, "aceleracao": 0.8, "forca": 1.1, "agilidade": 0.9
            },
            Posicao.MEIO_CAMPO: {
                "finalizacao": 0.8, "passe": 1.2, "drible": 1.0, "cruzamento": 0.8,
                "velocidade": 0.9, "aceleracao": 0.9, "forca": 0.9, "agilidade": 1.0
            },
            Posicao.MEIO_ATACANTE: {
                "finalizacao": 1.1, "passe": 1.1, "drible": 1.2, "cruzamento": 0.9,
                "velocidade": 1.0, "aceleracao": 1.0, "forca": 0.8, "agilidade": 1.1
            },
            Posicao.PONTA_DIREITA: {
                "finalizacao": 1.0, "passe": 0.9, "drible": 1.3, "cruzamento": 1.2,
                "velocidade": 1.2, "aceleracao": 1.2, "forca": 0.7, "agilidade": 1.2
            },
            Posicao.PONTA_ESQUERDA: {
                "finalizacao": 1.0, "passe": 0.9, "drible": 1.3, "cruzamento": 1.2,
                "velocidade": 1.2, "aceleracao": 1.2, "forca": 0.7, "agilidade": 1.2
            },
            Posicao.ATACANTE: {
                "finalizacao": 1.3, "passe": 0.8, "drible": 1.0, "cruzamento": 0.6,
                "velocidade": 1.0, "aceleracao": 1.0, "forca": 1.1, "agilidade": 1.0
            }
        }

        multiplicador = multiplicadores.get(posicao, {}).get(atributo, 1.0)
        valor_ajustado = int(base * multiplicador)
        return self._gerar_atributo(valor_ajustado, 10)

    def simular_partida(self, partida: Partida) -> Partida:
        """Simula uma partida entre dois times"""
        if partida.finalizada:
            return partida

        # Define escalações se não foram definidas
        if not partida.escalacao_casa:
            partida.escalacao_casa = partida.time_casa.escalacao_titular()
        if not partida.escalacao_visitante:
            partida.escalacao_visitante = partida.time_visitante.escalacao_titular()

        # Calcula força dos times
        forca_casa = self._calcular_forca_time(partida.escalacao_casa)
        forca_visitante = self._calcular_forca_time(partida.escalacao_visitante)

        # Vantagem de jogar em casa
        forca_casa *= 1.1

        # Simula gols baseado na força dos times
        gols_casa = self._simular_gols(forca_casa, forca_visitante)
        gols_visitante = self._simular_gols(forca_visitante, forca_casa)

        partida.gols_casa = gols_casa
        partida.gols_visitante = gols_visitante
        partida.finalizada = True

        # Atualiza estatísticas dos times
        self._atualizar_estatisticas_time(partida.time_casa, gols_casa, gols_visitante)
        self._atualizar_estatisticas_time(partida.time_visitante, gols_visitante, gols_casa)

        return partida

    def _calcular_forca_time(self, escalacao: List[Jogador]) -> float:
        """Calcula a força média de uma escalação"""
        if not escalacao:
            return 50.0

        forca_total = sum(jogador.media_geral() for jogador in escalacao)
        return forca_total / len(escalacao)

    def _simular_gols(self, forca_atacante: float, forca_defensiva: float) -> int:
        """Simula número de gols baseado na força dos times"""
        # Probabilidade base de gol
        prob_base = 0.02  # 2% por "tentativa"

        # Ajusta probabilidade baseada na diferença de força
        diferenca = forca_atacante - forca_defensiva
        multiplicador = 1 + (diferenca / 100)  # +/- 1% para cada ponto de diferença

        prob_gol = max(0.005, min(0.05, prob_base * multiplicador))

        # Simula 90 "tentativas" (minutos do jogo)
        gols = 0
        for _ in range(90):
            if random.random() < prob_gol:
                gols += 1

        return gols

    def _atualizar_estatisticas_time(self, time: Time, gols_marcados: int, gols_sofridos: int):
        """Atualiza estatísticas do time após uma partida"""
        time.jogos += 1
        time.gols_marcados += gols_marcados
        time.gols_sofridos += gols_sofridos

        if gols_marcados > gols_sofridos:
            time.vitorias += 1
        elif gols_marcados == gols_sofridos:
            time.empates += 1
        else:
            time.derrotas += 1

    def simular_rodada(self, liga: Liga, rodada: int):
        """Simula todas as partidas de uma rodada"""
        if rodada >= len(liga.rodadas):
            return

        for partida in liga.rodadas[rodada]:
            self.simular_partida(partida)

        liga.rodada_atual = rodada + 1

    def avancar_tempo(self, dias: int = 7):
        """Avança o tempo do jogo"""
        self.data_atual += datetime.timedelta(days=dias)

        # Atualiza lesões e suspensões
        for liga in self.ligas:
            for time in liga.times:
                for jogador in time.jogadores:
                    if jogador.lesao_dias > 0:
                        jogador.lesao_dias = max(0, jogador.lesao_dias - dias)
                        if jogador.lesao_dias == 0:
                            jogador.status = StatusJogador.ATIVO

                    if jogador.suspensao_jogos > 0 and liga.rodada_atual > 0:
                        # Suspensão diminui apenas quando há jogos
                        jogador.suspensao_jogos = max(0, jogador.suspensao_jogos - 1)

    def salvar_jogo(self, arquivo: str):
        """Salva o estado atual do jogo"""
        dados = {
            'data_atual': self.data_atual.isoformat(),
            'temporada_atual': self.temporada_atual,
            'time_jogador': self.time_jogador.nome if self.time_jogador else None,
            'ligas': []
        }

        for liga in self.ligas:
            liga_dados = {
                'nome': liga.nome,
                'pais': liga.pais,
                'nivel': liga.nivel,
                'rodada_atual': liga.rodada_atual,
                'temporada': liga.temporada,
                'times': []
            }

            for time in liga.times:
                time_dados = {
                    'nome': time.nome,
                    'cidade': time.cidade,
                    'pais': time.pais,
                    'fundacao': time.fundacao,
                    'estadio': time.estadio,
                    'capacidade_estadio': time.capacidade_estadio,
                    'orcamento': time.orcamento,
                    'jogos': time.jogos,
                    'vitorias': time.vitorias,
                    'empates': time.empates,
                    'derrotas': time.derrotas,
                    'gols_marcados': time.gols_marcados,
                    'gols_sofridos': time.gols_sofridos,
                    'jogadores': []
                }

                for jogador in time.jogadores:
                    jogador_dados = {
                        'nome': jogador.nome,
                        'idade': jogador.idade,
                        'posicao': jogador.posicao.value,
                        'nacionalidade': jogador.nacionalidade,
                        'finalizacao': jogador.finalizacao,
                        'passe': jogador.passe,
                        'drible': jogador.drible,
                        'primeiro_toque': jogador.primeiro_toque,
                        'cruzamento': jogador.cruzamento,
                        'velocidade': jogador.velocidade,
                        'aceleracao': jogador.aceleracao,
                        'forca': jogador.forca,
                        'resistencia': jogador.resistencia,
                        'agilidade': jogador.agilidade,
                        'concentracao': jogador.concentracao,
                        'decisao': jogador.decisao,
                        'lideranca': jogador.lideranca,
                        'trabalho_equipe': jogador.trabalho_equipe,
                        'reflexos': jogador.reflexos,
                        'posicionamento_gk': jogador.posicionamento_gk,
                        'manuseio': jogador.manuseio,
                        'status': jogador.status.value,
                        'valor_mercado': jogador.valor_mercado,
                        'lesao_dias': jogador.lesao_dias,
                        'suspensao_jogos': jogador.suspensao_jogos,
                        'moral': jogador.moral,
                        'forma': jogador.forma
                    }
                    time_dados['jogadores'].append(jogador_dados)

                liga_dados['times'].append(time_dados)

            dados['ligas'].append(liga_dados)

        with open(arquivo, 'w', encoding='utf-8') as f:
            json.dump(dados, f, indent=2, ensure_ascii=False)

    def carregar_jogo(self, arquivo: str):
        """Carrega um jogo salvo"""
        with open(arquivo, 'r', encoding='utf-8') as f:
            dados = json.load(f)

        self.data_atual = datetime.date.fromisoformat(dados['data_atual'])
        self.temporada_atual = dados['temporada_atual']
        self.ligas = []

        for liga_dados in dados['ligas']:
            liga = Liga(
                nome=liga_dados['nome'],
                pais=liga_dados['pais'],
                nivel=liga_dados['nivel']
            )
            liga.rodada_atual = liga_dados['rodada_atual']
            liga.temporada = liga_dados['temporada']

            for time_dados in liga_dados['times']:
                time = Time(
                    nome=time_dados['nome'],
                    cidade=time_dados['cidade'],
                    pais=time_dados['pais'],
                    fundacao=time_dados['fundacao'],
                    estadio=time_dados['estadio'],
                    capacidade_estadio=time_dados['capacidade_estadio']
                )

                time.orcamento = time_dados['orcamento']
                time.jogos = time_dados['jogos']
                time.vitorias = time_dados['vitorias']
                time.empates = time_dados['empates']
                time.derrotas = time_dados['derrotas']
                time.gols_marcados = time_dados['gols_marcados']
                time.gols_sofridos = time_dados['gols_sofridos']

                for jogador_dados in time_dados['jogadores']:
                    jogador = Jogador(
                        nome=jogador_dados['nome'],
                        idade=jogador_dados['idade'],
                        posicao=Posicao(jogador_dados['posicao']),
                        nacionalidade=jogador_dados['nacionalidade']
                    )

                    # Restaura todos os atributos
                    for attr in ['finalizacao', 'passe', 'drible', 'primeiro_toque', 'cruzamento',
                               'velocidade', 'aceleracao', 'forca', 'resistencia', 'agilidade',
                               'concentracao', 'decisao', 'lideranca', 'trabalho_equipe',
                               'reflexos', 'posicionamento_gk', 'manuseio', 'valor_mercado',
                               'lesao_dias', 'suspensao_jogos', 'moral', 'forma']:
                        setattr(jogador, attr, jogador_dados[attr])

                    jogador.status = StatusJogador(jogador_dados['status'])
                    time.jogadores.append(jogador)

                time.calcular_salarios()
                liga.times.append(time)

            self.ligas.append(liga)

        # Restaura time do jogador
        if dados['time_jogador']:
            for liga in self.ligas:
                for time in liga.times:
                    if time.nome == dados['time_jogador']:
                        self.time_jogador = time
                        break
