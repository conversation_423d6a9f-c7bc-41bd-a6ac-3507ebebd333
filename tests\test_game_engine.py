import random
from elifoot2025.game_engine import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Time
import pytest

class TestMotorJogo:
    def test_geracao_habilidades(self):
        motor = MotorJogo()
        random.seed(42)  # Resultados consistentes para testes
        
        # Testa limites válidos
        for forca in [1, 50, 100]:
            habilidades = motor.gerar_habilidades(forca)
            assert 1 <= habilidades['forca'] <= 100
            assert 1 <= habilidades['velocidade'] <= 100
            assert 1 <= habilidades['tecnica'] <= 100
        
        # Teste de consistência com seed
        habilidades = motor.gerar_habilidades(80)
        assert isinstance(habilidades['forca'], int)
        assert isinstance(habilidades['velocidade'], int)
        assert isinstance(habilidades['tecnica'], int)
    
    def test_criacao_time(self):
        jogadores = [Jo<PERSON>r("Jogador Teste", "ATA", 80, 75, 85)]
        time = Time("Time Teste", 80, jogador<PERSON>)
        
        assert time.nome == "Time Teste"
        assert len(time.jogador<PERSON>) == 1
