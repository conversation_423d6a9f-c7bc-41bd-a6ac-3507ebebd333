import requests
from bs4 import BeautifulSoup

class CarregadorDados:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.football-data.org/v4/"
    
    def obter_clubes_liga(self, liga_id):
        """Obtém lista de clubes de uma liga específica"""
        headers = {'X-Auth-Token': self.api_key}
        response = requests.get(f"{self.base_url}competitions/{liga_id}/teams", headers=headers)
        return response.json()['teams']
    
    def obter_classificacao_uefa(self):
        """Obtém ranking de clubes da UEFA"""
        url = "https://kassiesa.net/uefa/data/method5/rank2023.html"
        page = requests.get(url)
        soup = BeautifulSoup(page.content, 'html.parser')
        # Processamento do ranking
        return []
