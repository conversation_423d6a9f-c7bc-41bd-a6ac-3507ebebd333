import pytest
from unittest.mock import Mock, patch
from elifoot2025.data_loader import CarregadorDados


class TestCarregadorDados:
    def test_criacao_carregador(self):
        """Testa criação do carregador de dados"""
        carregador = CarregadorDados("test_api_key")
        
        assert carregador.api_key == "test_api_key"
        assert carregador.base_url == "https://api.football-data.org/v4/"
    
    @patch('elifoot2025.data_loader.requests.get')
    def test_obter_clubes_liga_sucesso(self, mock_get):
        """Testa obtenção de clubes com sucesso"""
        # Mock da resposta da API
        mock_response = Mock()
        mock_response.json.return_value = {
            'teams': [
                {'name': 'Flamengo', 'founded': 1895},
                {'name': 'Palmeiras', 'founded': 1914}
            ]
        }
        mock_get.return_value = mock_response
        
        carregador = CarregadorDados("test_key")
        resultado = carregador.obter_clubes_liga("BR1")
        
        assert len(resultado) == 2
        assert resultado[0]['name'] == 'Flamengo'
        mock_get.assert_called_once_with(
            "https://api.football-data.org/v4/competitions/BR1/teams",
            headers={'X-Auth-Token': 'test_key'}
        )
    
    @patch('elifoot2025.data_loader.requests.get')
    def test_obter_classificacao_uefa(self, mock_get):
        """Testa obtenção de classificação UEFA"""
        # Mock da resposta
        mock_response = Mock()
        mock_response.content = "<html><body>Test content</body></html>"
        mock_get.return_value = mock_response
        
        carregador = CarregadorDados("test_key")
        resultado = carregador.obter_classificacao_uefa()
        
        # Por enquanto retorna lista vazia (implementação básica)
        assert resultado == []
        mock_get.assert_called_once_with("https://kassiesa.net/uefa/data/method5/rank2023.html")
    
    @patch('elifoot2025.data_loader.requests.get')
    def test_obter_clubes_liga_erro(self, mock_get):
        """Testa tratamento de erro na API"""
        # Mock de erro na requisição
        mock_get.side_effect = Exception("Erro de conexão")
        
        carregador = CarregadorDados("test_key")
        
        # Deve propagar a exceção
        with pytest.raises(Exception):
            carregador.obter_clubes_liga("BR1")
