import random
import datetime
import tempfile
import os
from elifoot2025.game_engine import (
    MotorJogo, Jogador, Time, Liga, Partida, Contrato,
    Posicao, StatusJogador, FORMACOES
)
import pytest


class TestJogador:
    def test_criacao_jogador_basico(self):
        """Testa criação básica de jogador"""
        jogador = Jogador(
            nome="Pelé",
            idade=25,
            posicao=Posicao.ATACANTE,
            nacionalidade="Brasil"
        )
        
        assert jogador.nome == "Pelé"
        assert jogador.idade == 25
        assert jogador.posicao == Posicao.ATACANTE
        assert jogador.nacionalidade == "Brasil"
        assert jogador.contrato is not None
        assert jogador.esta_disponivel()
    
    def test_media_geral_jogador_campo(self):
        """Testa cálculo de média geral para jogador de campo"""
        jogador = Jogador(
            nome="Teste",
            idade=25,
            posicao=Posicao.MEIO_CAMPO,
            nacionalidade="Brasil",
            finalizacao=80,
            passe=85,
            drible=75,
            primeiro_toque=80,
            cruzamento=70,
            velocidade=75,
            aceleracao=75,
            forca=70,
            resistencia=80,
            agilidade=75,
            concentracao=80,
            decisao=75,
            lideranca=70,
            trabalho_equipe=85
        )
        
        media = jogador.media_geral()
        assert 70 <= media <= 85  # Deve estar na faixa esperada
        assert isinstance(media, int)
    
    def test_media_geral_goleiro(self):
        """Testa cálculo de média geral para goleiro"""
        goleiro = Jogador(
            nome="Goleiro Teste",
            idade=28,
            posicao=Posicao.GOLEIRO,
            nacionalidade="Brasil",
            reflexos=85,
            posicionamento_gk=80,
            manuseio=82,
            concentracao=78,
            decisao=75
        )
        
        media = goleiro.media_geral()
        assert 75 <= media <= 85
        assert isinstance(media, int)
    
    def test_disponibilidade_jogador(self):
        """Testa disponibilidade do jogador"""
        jogador = Jogador("Teste", 25, Posicao.ATACANTE, "Brasil")
        
        # Jogador normal deve estar disponível
        assert jogador.esta_disponivel()
        
        # Jogador lesionado não deve estar disponível
        jogador.lesao_dias = 5
        assert not jogador.esta_disponivel()
        
        # Jogador suspenso não deve estar disponível
        jogador.lesao_dias = 0
        jogador.suspensao_jogos = 2
        assert not jogador.esta_disponivel()


class TestContrato:
    def test_contrato_vencido(self):
        """Testa verificação de contrato vencido"""
        contrato_vencido = Contrato(
            salario_semanal=5000,
            data_inicio=datetime.date(2023, 1, 1),
            data_fim=datetime.date(2023, 12, 31),
            clausula_rescisao=1000000
        )
        
        assert contrato_vencido.esta_vencido()
        assert contrato_vencido.semanas_restantes() == 0
    
    def test_contrato_valido(self):
        """Testa contrato ainda válido"""
        contrato_valido = Contrato(
            salario_semanal=5000,
            data_inicio=datetime.date.today(),
            data_fim=datetime.date.today() + datetime.timedelta(days=365),
            clausula_rescisao=1000000
        )
        
        assert not contrato_valido.esta_vencido()
        assert contrato_valido.semanas_restantes() > 0


class TestTime:
    def test_criacao_time(self):
        """Testa criação de time"""
        jogadores = [
            Jogador("Jogador 1", 25, Posicao.ATACANTE, "Brasil"),
            Jogador("Jogador 2", 27, Posicao.MEIO_CAMPO, "Brasil")
        ]
        
        time = Time(
            nome="Flamengo",
            cidade="Rio de Janeiro",
            pais="Brasil",
            fundacao=1895,
            estadio="Maracanã",
            capacidade_estadio=78838,
            jogadores=jogadores
        )
        
        assert time.nome == "Flamengo"
        assert len(time.jogadores) == 2
        assert time.salarios_semanais > 0
    
    def test_estatisticas_time(self):
        """Testa cálculos de estatísticas do time"""
        time = Time("Teste", "Cidade", "País", 1900, "Estádio", 40000)
        
        # Simula algumas estatísticas
        time.vitorias = 10
        time.empates = 5
        time.derrotas = 3
        time.gols_marcados = 25
        time.gols_sofridos = 15
        
        assert time.pontos() == 35  # 10*3 + 5*1
        assert time.saldo_gols() == 10  # 25 - 15
    
    def test_escalacao_titular(self):
        """Testa geração de escalação titular"""
        motor = MotorJogo()
        time = Time("Teste", "Cidade", "País", 1900, "Estádio", 40000)
        time.jogadores = motor.gerar_elenco_completo("Teste", 75)
        
        escalacao = time.escalacao_titular()
        
        assert len(escalacao) <= 11
        # Deve ter pelo menos um goleiro
        goleiros = [j for j in escalacao if j.posicao == Posicao.GOLEIRO]
        assert len(goleiros) >= 1


class TestLiga:
    def test_criacao_liga(self):
        """Testa criação de liga"""
        liga = Liga("Brasileirão", "Brasil", 1)
        
        assert liga.nome == "Brasileirão"
        assert liga.pais == "Brasil"
        assert liga.nivel == 1
        assert liga.rodada_atual == 0
    
    def test_tabela_classificacao(self):
        """Testa geração de tabela de classificação"""
        liga = Liga("Teste", "Brasil", 1)
        
        # Cria times com estatísticas diferentes
        time1 = Time("Time A", "Cidade A", "Brasil", 1900, "Estádio A", 40000)
        time1.vitorias = 10
        time1.empates = 2
        time1.gols_marcados = 30
        time1.gols_sofridos = 10
        
        time2 = Time("Time B", "Cidade B", "Brasil", 1900, "Estádio B", 40000)
        time2.vitorias = 8
        time2.empates = 4
        time2.gols_marcados = 25
        time2.gols_sofridos = 15
        
        liga.times = [time1, time2]
        tabela = liga.gerar_tabela_classificacao()
        
        assert len(tabela) == 2
        assert tabela[0]['time'] == "Time A"  # Mais pontos
        assert tabela[0]['posicao'] == 1
        assert tabela[1]['posicao'] == 2
    
    def test_geracao_calendario(self):
        """Testa geração de calendário"""
        liga = Liga("Teste", "Brasil", 1)
        
        # Adiciona 4 times para teste
        for i in range(4):
            time = Time(f"Time {i+1}", f"Cidade {i+1}", "Brasil", 1900, f"Estádio {i+1}", 40000)
            liga.times.append(time)
        
        liga.gerar_calendario()
        
        # Com 4 times, deve ter 6 rodadas (3 no turno + 3 no returno)
        assert len(liga.rodadas) == 6
        
        # Cada rodada deve ter 2 partidas (4 times / 2)
        for rodada in liga.rodadas:
            assert len(rodada) == 2


class TestMotorJogo:
    def test_criacao_motor(self):
        """Testa criação do motor do jogo"""
        motor = MotorJogo()
        
        assert motor.temporada_atual == 2025
        assert isinstance(motor.data_atual, datetime.date)
        assert len(motor.ligas) == 0
    
    def test_geracao_elenco_completo(self):
        """Testa geração de elenco completo"""
        motor = MotorJogo()
        elenco = motor.gerar_elenco_completo("Flamengo", 80)
        
        # Deve ter 25 jogadores (3 goleiros + 8 defensores + 8 meio-campistas + 6 atacantes)
        assert len(elenco) == 25
        
        # Verifica se tem jogadores de todas as posições principais
        posicoes = [j.posicao for j in elenco]
        assert Posicao.GOLEIRO in posicoes
        assert Posicao.ZAGUEIRO in posicoes
        assert Posicao.MEIO_CAMPO in posicoes
        assert Posicao.ATACANTE in posicoes
    
    def test_geracao_jogador(self):
        """Testa geração individual de jogador"""
        motor = MotorJogo()
        jogador = motor.gerar_jogador("Teste", Posicao.ATACANTE, 80)
        
        assert jogador.nome == "Teste"
        assert jogador.posicao == Posicao.ATACANTE
        assert 18 <= jogador.idade <= 35
        assert jogador.valor_mercado > 0
        assert 1 <= jogador.media_geral() <= 99

    def test_criacao_liga_exemplo(self):
        """Testa criação de liga de exemplo"""
        motor = MotorJogo()
        motor.criar_liga_exemplo()

        assert len(motor.ligas) == 1
        liga = motor.ligas[0]
        assert len(liga.times) == 20
        assert len(liga.rodadas) > 0

        # Verifica se todos os times têm elencos completos
        for time in liga.times:
            assert len(time.jogadores) == 25


class TestPartida:
    def test_criacao_partida(self):
        """Testa criação de partida"""
        time1 = Time("Flamengo", "Rio", "Brasil", 1895, "Maracanã", 78000)
        time2 = Time("Palmeiras", "São Paulo", "Brasil", 1914, "Allianz", 43000)
        data = datetime.date.today()

        partida = Partida(time1, time2, data)

        assert partida.time_casa == time1
        assert partida.time_visitante == time2
        assert partida.data == data
        assert not partida.finalizada
        assert partida.vencedor() is None

    def test_simulacao_partida(self):
        """Testa simulação de partida"""
        motor = MotorJogo()

        # Cria times com elencos
        time1 = Time("Time A", "Cidade A", "Brasil", 1900, "Estádio A", 40000)
        time1.jogadores = motor.gerar_elenco_completo("Time A", 80)

        time2 = Time("Time B", "Cidade B", "Brasil", 1900, "Estádio B", 40000)
        time2.jogadores = motor.gerar_elenco_completo("Time B", 70)

        partida = Partida(time1, time2, datetime.date.today())

        # Simula a partida
        motor.simular_partida(partida)

        assert partida.finalizada
        assert partida.gols_casa >= 0
        assert partida.gols_visitante >= 0
        assert len(partida.escalacao_casa) > 0
        assert len(partida.escalacao_visitante) > 0

        # Verifica se as estatísticas dos times foram atualizadas
        assert time1.jogos == 1
        assert time2.jogos == 1

    def test_simulacao_rodada(self):
        """Testa simulação de uma rodada completa"""
        motor = MotorJogo()
        motor.criar_liga_exemplo()

        liga = motor.ligas[0]
        rodada_inicial = liga.rodada_atual

        # Simula primeira rodada
        motor.simular_rodada(liga, 0)

        assert liga.rodada_atual == rodada_inicial + 1

        # Verifica se todas as partidas da rodada foram simuladas
        for partida in liga.rodadas[0]:
            assert partida.finalizada


class TestSaveLoad:
    def test_salvar_carregar_jogo(self):
        """Testa salvamento e carregamento do jogo"""
        motor_original = MotorJogo()
        motor_original.criar_liga_exemplo()
        motor_original.temporada_atual = 2026

        # Simula algumas rodadas
        liga = motor_original.ligas[0]
        motor_original.simular_rodada(liga, 0)
        motor_original.simular_rodada(liga, 1)

        # Salva o jogo
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            arquivo_temp = f.name

        try:
            motor_original.salvar_jogo(arquivo_temp)

            # Carrega o jogo em um novo motor
            motor_carregado = MotorJogo()
            motor_carregado.carregar_jogo(arquivo_temp)

            # Verifica se os dados foram carregados corretamente
            assert motor_carregado.temporada_atual == 2026
            assert len(motor_carregado.ligas) == 1

            liga_carregada = motor_carregado.ligas[0]
            assert len(liga_carregada.times) == 20
            assert liga_carregada.rodada_atual == 2

            # Verifica se as estatísticas dos times foram preservadas
            for time_original, time_carregado in zip(liga.times, liga_carregada.times):
                assert time_original.nome == time_carregado.nome
                assert time_original.jogos == time_carregado.jogos
                assert time_original.vitorias == time_carregado.vitorias
                assert len(time_original.jogadores) == len(time_carregado.jogadores)

        finally:
            # Limpa arquivo temporário
            if os.path.exists(arquivo_temp):
                os.unlink(arquivo_temp)


class TestAvanceTempo:
    def test_avanco_tempo(self):
        """Testa avanço do tempo no jogo"""
        motor = MotorJogo()
        data_inicial = motor.data_atual

        motor.avancar_tempo(7)  # Avança uma semana

        assert motor.data_atual == data_inicial + datetime.timedelta(days=7)

    def test_recuperacao_lesao(self):
        """Testa recuperação de lesão com avanço do tempo"""
        motor = MotorJogo()
        motor.criar_liga_exemplo()

        # Pega um jogador e o lesiona
        time = motor.ligas[0].times[0]
        jogador = time.jogadores[0]
        jogador.lesao_dias = 14
        jogador.status = StatusJogador.LESIONADO

        assert not jogador.esta_disponivel()

        # Avança tempo
        motor.avancar_tempo(7)  # Uma semana
        assert jogador.lesao_dias == 7
        assert not jogador.esta_disponivel()

        motor.avancar_tempo(7)  # Mais uma semana
        assert jogador.lesao_dias == 0
        assert jogador.esta_disponivel()


class TestFormacoes:
    def test_formacoes_predefinidas(self):
        """Testa formações pré-definidas"""
        assert "4-4-2" in FORMACOES
        assert "4-3-3" in FORMACOES
        assert "3-5-2" in FORMACOES

        formacao_442 = FORMACOES["4-4-2"]
        assert formacao_442.defensores == 4
        assert formacao_442.meio_campistas == 4
        assert formacao_442.atacantes == 2
        assert len(formacao_442.posicoes) == 11  # 10 jogadores de campo + goleiro

    def test_validacao_formacao(self):
        """Testa validação de formação"""
        from elifoot2025.game_engine import Formacao

        # Formação válida
        formacao_valida = Formacao("4-4-2", 4, 4, 2, [(0, 0)] * 11)
        assert formacao_valida.defensores + formacao_valida.meio_campistas + formacao_valida.atacantes == 10

        # Formação inválida deve gerar erro
        with pytest.raises(ValueError):
            Formacao("Inválida", 5, 5, 5, [(0, 0)] * 11)  # 15 jogadores de campo


class TestEdgeCases:
    def test_carregar_dados_com_erro(self):
        """Testa carregamento de dados com erro na API"""
        from unittest.mock import Mock

        motor = MotorJogo()
        mock_carregador = Mock()
        mock_carregador.obter_clubes_liga.side_effect = Exception("Erro de API")
        motor.carregador_dados = mock_carregador

        # Deve criar liga de exemplo quando há erro
        motor.carregar_dados("test_liga")
        assert len(motor.ligas) == 1
        assert len(motor.ligas[0].times) == 20

    def test_time_sem_jogadores_escalacao(self):
        """Testa escalação de time sem jogadores"""
        time = Time("Teste", "Cidade", "País", 1900, "Estádio", 40000)
        escalacao = time.escalacao_titular()

        assert len(escalacao) == 0

    def test_partida_ja_finalizada(self):
        """Testa simulação de partida já finalizada"""
        motor = MotorJogo()
        time1 = Time("Time A", "Cidade A", "Brasil", 1900, "Estádio A", 40000)
        time2 = Time("Time B", "Cidade B", "Brasil", 1900, "Estádio B", 40000)

        partida = Partida(time1, time2, datetime.date.today())
        partida.finalizada = True
        partida.gols_casa = 2
        partida.gols_visitante = 1

        # Simular partida já finalizada não deve alterar resultado
        resultado_original = partida.resultado()
        motor.simular_partida(partida)
        assert partida.resultado() == resultado_original

    def test_simulacao_rodada_inexistente(self):
        """Testa simulação de rodada que não existe"""
        motor = MotorJogo()
        liga = Liga("Teste", "Brasil", 1)

        # Tentar simular rodada 999 (não existe)
        motor.simular_rodada(liga, 999)
        assert liga.rodada_atual == 0  # Não deve alterar

    def test_jogador_com_atributos_extremos(self):
        """Testa jogador com atributos nos extremos"""
        motor = MotorJogo()

        # Testa com força muito baixa
        jogador_fraco = motor.gerar_jogador("Fraco", Posicao.ATACANTE, 1)
        assert 1 <= jogador_fraco.media_geral() <= 99

        # Testa com força muito alta
        jogador_forte = motor.gerar_jogador("Forte", Posicao.ATACANTE, 99)
        assert 1 <= jogador_forte.media_geral() <= 99

    def test_calcular_forca_time_vazio(self):
        """Testa cálculo de força de time sem escalação"""
        motor = MotorJogo()
        forca = motor._calcular_forca_time([])
        assert forca == 50.0

    def test_liga_com_numero_impar_times(self):
        """Testa liga com número ímpar de times"""
        liga = Liga("Teste", "Brasil", 1)

        # Adiciona 5 times (número ímpar)
        for i in range(5):
            time = Time(f"Time {i+1}", f"Cidade {i+1}", "Brasil", 1900, f"Estádio {i+1}", 40000)
            liga.times.append(time)

        liga.gerar_calendario()

        # Deve funcionar mesmo com número ímpar
        assert len(liga.rodadas) > 0

        # Verifica se não há jogos com None (time fantasma)
        for rodada in liga.rodadas:
            for partida in rodada:
                assert partida.time_casa is not None
                assert partida.time_visitante is not None

    def test_carregar_dados_api_real(self):
        """Testa carregamento com dados de API real (mock)"""
        from unittest.mock import Mock

        motor = MotorJogo()
        mock_carregador = Mock()
        mock_carregador.obter_clubes_liga.return_value = [
            {
                'name': 'Real Madrid',
                'area': {'name': 'Spain'},
                'founded': 1902,
                'venue': 'Santiago Bernabéu'
            }
        ]
        motor.carregador_dados = mock_carregador

        motor.carregar_dados("ES1")

        assert len(motor.ligas) == 1
        assert len(motor.ligas[0].times) == 1
        assert motor.ligas[0].times[0].nome == 'Real Madrid'
