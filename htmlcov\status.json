{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "9f484bd0c27fe79010e234ff8720424b", "files": {"z_b64fc5cdc19abde2___init___py": {"hash": "1c63305f975f0f2e2ed707e42d9672dd", "index": {"url": "z_b64fc5cdc19abde2___init___py.html", "file": "elifoot2025\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b64fc5cdc19abde2_data_loader_py": {"hash": "98968bcfcea38fd05ce402735b2b903d", "index": {"url": "z_b64fc5cdc19abde2_data_loader_py.html", "file": "elifoot2025\\data_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b64fc5cdc19abde2_game_engine_py": {"hash": "762159e62f01b40d0c8a24d46e5cf40d", "index": {"url": "z_b64fc5cdc19abde2_game_engine_py.html", "file": "elifoot2025\\game_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 412, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}