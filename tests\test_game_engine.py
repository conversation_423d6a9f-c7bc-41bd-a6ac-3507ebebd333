import random
from elifoot2025.game_engine import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Posicao
import pytest

class TestMotorJogo:
    def test_geracao_jogador_atributos(self):
        """Testa geração de jogador com atributos válidos"""
        motor = MotorJogo()
        random.seed(42)  # Resultados consistentes para testes

        # Testa geração de jogadores para diferentes forças de time
        for forca in [1, 50, 100]:
            jogador = motor.gerar_jogador("Teste", Posicao.ATACANTE, forca)
            assert 1 <= jogador.finalizacao <= 99
            assert 1 <= jogador.velocidade <= 99
            assert 1 <= jogador.passe <= 99
            assert 1 <= jogador.media_geral() <= 99

        # Teste de consistência com seed
        jogador = motor.gerar_jogador("Teste Consistente", Posicao.MEIO_CAMPO, 80)
        assert isinstance(jogador.finalizacao, int)
        assert isinstance(jogador.velocidade, int)
        assert isinstance(jogador.passe, int)

    def test_criaca<PERSON>_time_completo(self):
        """Testa criação de time com nova estrutura"""
        jogador = <PERSON><PERSON><PERSON>("Jogador Teste", 25, <PERSON>sicao.ATACANTE, "Brasil")
        time = Time(
            nome="Time Teste",
            cidade="Cidade Teste",
            pais="Brasil",
            fundacao=1900,
            estadio="Estádio Teste",
            capacidade_estadio=40000,
            jogadores=[jogador]
        )

        assert time.nome == "Time Teste"
        assert len(time.jogadores) == 1
        assert time.cidade == "Cidade Teste"
        assert time.salarios_semanais > 0
