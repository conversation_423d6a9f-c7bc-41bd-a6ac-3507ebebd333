<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">96%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 23:01 -0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2___init___py.html">elifoot2025\__init__.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html#t5">elifoot2025\data_loader.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html#t5"><data value='init__'>CarregadorDados.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html#t9">elifoot2025\data_loader.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html#t9"><data value='obter_clubes_liga'>CarregadorDados.obter_clubes_liga</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html#t15">elifoot2025\data_loader.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html#t15"><data value='obter_classificacao_uefa'>CarregadorDados.obter_classificacao_uefa</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html">elifoot2025\data_loader.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_data_loader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t40">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t40"><data value='esta_vencido'>Contrato.esta_vencido</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t43">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t43"><data value='semanas_restantes'>Contrato.semanas_restantes</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t92">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t92"><data value='post_init__'>Jogador.__post_init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t102">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t102"><data value='media_geral'>Jogador.media_geral</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t118">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t118"><data value='esta_disponivel'>Jogador.esta_disponivel</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t134">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t134"><data value='post_init__'>Formacao.__post_init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t187">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t187"><data value='post_init__'>Time.__post_init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t190">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t190"><data value='calcular_salarios'>Time.calcular_salarios</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t194">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t194"><data value='pontos'>Time.pontos</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t198">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t198"><data value='saldo_gols'>Time.saldo_gols</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t202">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t202"><data value='media_idade'>Time.media_idade</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t208">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t208"><data value='valor_elenco'>Time.valor_elenco</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t212">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t212"><data value='jogadores_por_posicao'>Time.jogadores_por_posicao</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t216">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t216"><data value='escalacao_titular'>Time.escalacao_titular</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t258">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t258"><data value='resultado'>Partida.resultado</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t264">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t264"><data value='vencedor'>Partida.vencedor</data></a></td>
                <td>7</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="2 7">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t286">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t286"><data value='gerar_tabela_classificacao'>Liga.gerar_tabela_classificacao</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t311">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t311"><data value='gerar_calendario'>Liga.gerar_calendario</data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t355">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t355"><data value='init__'>MotorJogo.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t362">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t362"><data value='carregar_dados'>MotorJogo.carregar_dados</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t380">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t380"><data value='criar_liga_exemplo'>MotorJogo.criar_liga_exemplo</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t410">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t410"><data value='criar_time_com_jogadores'>MotorJogo.criar_time_com_jogadores</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t428">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t428"><data value='gerar_elenco_completo'>MotorJogo.gerar_elenco_completo</data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t489">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t489"><data value='gerar_jogador'>MotorJogo.gerar_jogador</data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t549">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t549"><data value='gerar_atributo'>MotorJogo._gerar_atributo</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t553">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t553"><data value='gerar_atributo_posicional'>MotorJogo._gerar_atributo_posicional</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t599">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t599"><data value='simular_partida'>MotorJogo.simular_partida</data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t631">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t631"><data value='calcular_forca_time'>MotorJogo._calcular_forca_time</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t639">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t639"><data value='simular_gols'>MotorJogo._simular_gols</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t658">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t658"><data value='atualizar_estatisticas_time'>MotorJogo._atualizar_estatisticas_time</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t671">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t671"><data value='simular_rodada'>MotorJogo.simular_rodada</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t681">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t681"><data value='avancar_tempo'>MotorJogo.avancar_tempo</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t698">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t698"><data value='salvar_jogo'>MotorJogo.salvar_jogo</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t774">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html#t774"><data value='carregar_jogo'>MotorJogo.carregar_jogo</data></a></td>
                <td>33</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="28 33">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html">elifoot2025\game_engine.py</a></td>
                <td class="name left"><a href="z_b64fc5cdc19abde2_game_engine_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>140</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="140 140">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>428</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="412 428">96%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 23:01 -0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
